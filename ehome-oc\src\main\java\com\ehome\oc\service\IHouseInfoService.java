package com.ehome.oc.service;

import com.ehome.oc.domain.HouseInfo;
import com.jfinal.plugin.activerecord.Record;

public interface IHouseInfoService {

    public HouseInfo recordToObj(Record record);


    /**
     * 新增房屋
     */
    int addHouse(HouseInfo house);


    /**
     * 删除房屋
     */
    int deleteHouse(Long houseId);

    /**
     * 根据ID查询房屋
     */
    HouseInfo selectHouseById(Long houseId);

    /**
     * 统计用户的房屋数量
     */
    int countHouseByUserId(String  ownerId);

    /**
     * 设置默认房屋
     * @param houseId 房屋ID
     * @param wxUserId 用户ID
     * @return 影响的行数
     */
    int setDefaultHouse(Long houseId, String ownerId);

    void updateOwnerHouseInfo(String ownerId);

    void updateHouseOwnerInfo(String houseId);

    /**
     * 导入房屋数据
     *
     * @param houseList 房屋数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importHouse(java.util.List<HouseInfo> houseList, Boolean isUpdateSupport, String operName);

    /**
     * 刷新房屋全称字段
     * @param communityId 小区ID，为空则刷新所有小区
     */
    void refreshCombineName(String communityId);

    /**
     * 通过业主房屋关联表刷新所有统计字段和字符串字段
     * @param communityId 小区ID，为空则刷新所有小区
     */
    void refreshAllStatistics(String communityId);
}