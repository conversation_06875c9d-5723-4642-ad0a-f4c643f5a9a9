import os
import json
import uuid
from datetime import datetime

def sql_str(val):
    """SQL值转义函数"""
    if val is None:
        return 'NULL'
    if isinstance(val, (int, float)):
        return str(val)
    return "'{}'".format(str(val).replace("'", "''"))

def generate_id():
    """生成32位ID"""
    return str(uuid.uuid4()).replace('-', '')

def get_current_time():
    """获取当前时间字符串"""
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def main():
    # 获取脚本当前目录
    base_dir = os.path.dirname(__file__)
    data_file = os.path.join(base_dir, 'data.txt')

    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"未找到 {data_file}，请确认 data.txt 和 vehicle_data.py 在同一目录下。")
        return

    vehicle_sqls = []
    vehicle_house_rel_sqls = []
    vehicle_owner_rel_sqls = []

    current_time = get_current_time()

    for line in lines:
        if not line.strip():
            continue
        try:
            obj = json.loads(line)
        except Exception as e:
            print(f"解析JSON失败: {e}\n内容: {line[:100]}")
            continue

        # 处理车辆数据 - 适配现有数据格式
        vehicles = []
        if obj.get('code') == 0 and obj.get('data') and obj['data'].get('list'):
            vehicles = obj['data']['list']

        for vehicle_data in vehicles:
            # 生成车辆基本信息 - 适配现有数据格式
            vehicle_id = str(vehicle_data.get('id', generate_id()))
            community_id = str(vehicle_data.get('communityId', ''))
            plate_no = vehicle_data.get('licence', '')
            vehicle_type = str(vehicle_data.get('type', 1))  # 默认业主车辆
            vehicle_brand = ''  # 原数据中没有品牌信息
            vehicle_model = ''  # 原数据中没有型号信息
            owner_count = 1  # 默认1个业主
            check_status = 1  # 默认已审核
            remark = vehicle_data.get('remark', '')
            create_by = 'system'
            create_time = current_time
            update_by = 'system'
            update_time = current_time
            plate_type = ''  # 原数据中没有车牌类型

            # 从userItem获取业主信息
            user_item = vehicle_data.get('userItem', {})
            owner_id = str(user_item.get('id', '')) if user_item.get('id') else ''
            owner_name = user_item.get('name', '')

            # 从houseItem获取房屋信息
            house_item = vehicle_data.get('houseItem', {})
            house_id = str(house_item.get('id', '')) if house_item.get('id') else ''
            house_name = house_item.get('name', '')

            parking_space = ''  # 原数据中没有车位信息
            is_owner_ticket = 0
            parking_space_id = ''
            owner_real_name = vehicle_data.get('ownerName', '')
            owner_phone = vehicle_data.get('ownerPhone', '')

            # 生成eh_vehicle的INSERT语句
            vehicle_sql = f"""INSERT INTO eh_vehicle (vehicle_id, community_id, plate_no, vehicle_type, vehicle_brand, vehicle_model, owner_count, check_status, remark, create_by, create_time, update_by, update_time, plate_type, owner_id, owner_name, parking_space, is_owner_ticket, parking_space_id, house_id, house_name, owner_real_name, owner_phone) VALUES ({sql_str(vehicle_id)}, {sql_str(community_id)}, {sql_str(plate_no)}, {sql_str(vehicle_type)}, {sql_str(vehicle_brand)}, {sql_str(vehicle_model)}, {sql_str(owner_count)}, {sql_str(check_status)}, {sql_str(remark)}, {sql_str(create_by)}, {sql_str(create_time)}, {sql_str(update_by)}, {sql_str(update_time)}, {sql_str(plate_type)}, {sql_str(owner_id)}, {sql_str(owner_name)}, {sql_str(parking_space)}, {sql_str(is_owner_ticket)}, {sql_str(parking_space_id)}, {sql_str(house_id)}, {sql_str(house_name)}, {sql_str(owner_real_name)}, {sql_str(owner_phone)});"""
            vehicle_sqls.append(vehicle_sql)

            # 处理车辆-房屋关联关系
            if house_id:
                rel_id = generate_id()
                is_default = 1  # 默认关联
                rel_check_status = 1
                rel_remark = ''
                rel_create_by = 'system'
                rel_create_time = current_time
                rel_update_by = 'system'
                rel_update_time = current_time

                house_rel_sql = f"""INSERT INTO eh_vehicle_house_rel (rel_id, vehicle_id, house_id, is_default, check_status, remark, create_by, create_time, update_by, update_time) VALUES ({sql_str(rel_id)}, {sql_str(vehicle_id)}, {sql_str(house_id)}, {sql_str(is_default)}, {sql_str(rel_check_status)}, {sql_str(rel_remark)}, {sql_str(rel_create_by)}, {sql_str(rel_create_time)}, {sql_str(rel_update_by)}, {sql_str(rel_update_time)});"""
                vehicle_house_rel_sqls.append(house_rel_sql)

            # 处理车辆-业主关联关系
            if owner_id:
                rel_id = generate_id()
                is_default = 1  # 默认关联
                rel_check_status = 1
                rel_remark = ''
                rel_create_by = 'system'
                rel_create_time = current_time
                rel_update_by = 'system'
                rel_update_time = current_time

                owner_rel_sql = f"""INSERT INTO eh_vehicle_owner_rel (rel_id, vehicle_id, owner_id, is_default, check_status, remark, create_by, create_time, update_by, update_time) VALUES ({sql_str(rel_id)}, {sql_str(vehicle_id)}, {sql_str(owner_id)}, {sql_str(is_default)}, {sql_str(rel_check_status)}, {sql_str(rel_remark)}, {sql_str(rel_create_by)}, {sql_str(rel_create_time)}, {sql_str(rel_update_by)}, {sql_str(rel_update_time)});"""
                vehicle_owner_rel_sqls.append(owner_rel_sql)

    # 输出所有 SQL
    output_file = os.path.join(base_dir, 'output.sql')
    with open(output_file, 'w', encoding='utf-8') as f:
        # 写入表头注释
        f.write('-- 车辆信息表数据\n')
        for sql in vehicle_sqls:
            f.write(sql + '\n')

        f.write('\n-- 车辆房屋关联表数据\n')
        for sql in vehicle_house_rel_sqls:
            f.write(sql + '\n')

        f.write('\n-- 车辆业主关联表数据\n')
        for sql in vehicle_owner_rel_sqls:
            f.write(sql + '\n')

    print(f"SQL语句已生成到: {output_file}")
    print(f"共生成 {len(vehicle_sqls)} 条车辆记录")
    print(f"共生成 {len(vehicle_house_rel_sqls)} 条车辆-房屋关联记录")
    print(f"共生成 {len(vehicle_owner_rel_sqls)} 条车辆-业主关联记录")

if __name__ == '__main__':
    main()
