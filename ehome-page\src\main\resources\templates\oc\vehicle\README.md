# 车辆数据解析脚本使用说明

## 概述
本脚本用于解析车辆相关的JSON数据，并生成三个表的INSERT SQL语句：
- `eh_vehicle` - 车辆信息表
- `eh_vehicle_house_rel` - 车辆房屋关联表
- `eh_vehicle_owner_rel` - 车辆业主关联表

## 文件说明
- `vehicle_data.py` - 主要的数据解析脚本
- `data.txt` - 车辆数据文件（已存在）
- `output.sql` - 生成的SQL文件

## 使用方法

### 1. 数据文件格式
脚本读取 `data.txt` 文件，每行一个JSON对象，格式如下：

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "list": [
      {
        "id": 328956,
        "licence": "京Q9TP70",
        "communityId": 10772,
        "type": 1,
        "typeName": "业主车辆",
        "ownerName": "李星",
        "ownerPhone": "13903840002",
        "remark": "",
        "userItem": {
          "id": 1423878,
          "name": "李星(13903840002)"
        },
        "houseItem": {
          "id": 1999032,
          "name": "东区2号楼/1单元/2H"
        }
      }
    ]
  }
}
```

### 2. 字段映射说明

脚本会自动将JSON数据映射到数据库字段：

#### eh_vehicle表字段映射
- `vehicle_id` ← `id`
- `community_id` ← `communityId`
- `plate_no` ← `licence`
- `vehicle_type` ← `type`
- `owner_real_name` ← `ownerName`
- `owner_phone` ← `ownerPhone`
- `owner_id` ← `userItem.id`
- `owner_name` ← `userItem.name`
- `house_id` ← `houseItem.id`
- `house_name` ← `houseItem.name`
- `remark` ← `remark`

#### 关联表自动生成
- 如果存在`house_id`，自动生成`eh_vehicle_house_rel`记录
- 如果存在`owner_id`，自动生成`eh_vehicle_owner_rel`记录
- 关联ID使用UUID自动生成

### 3. 运行脚本
```bash
python vehicle_data.py
```

### 4. 输出结果
脚本会生成 `output.sql` 文件，包含三个表的INSERT语句，并在控制台显示统计信息。

## 运行示例

基于现有的`data.txt`文件，脚本成功解析了111条车辆记录：
- 生成111条车辆信息记录
- 生成111条车辆-房屋关联记录
- 生成111条车辆-业主关联记录

## 注意事项

1. **ID生成**: 关联表的ID使用32位UUID自动生成
2. **时间字段**: 创建时间和更新时间使用当前时间
3. **默认值**: 车辆类型默认为1（业主车辆），审核状态默认为1（已审核）
4. **数据验证**: 脚本会跳过无法解析的JSON行并输出错误信息
5. **字符转义**: 所有字符串值会自动进行SQL转义处理
6. **文件编码**: 确保`data.txt`文件使用UTF-8编码

## 脚本特点

- 基于参考的`data.py`脚本模式开发
- 适配现有车辆数据JSON格式
- 自动处理字段映射和类型转换
- 生成完整的三表关联数据
- 提供详细的执行统计信息

## 扩展说明

如需修改字段映射或添加新功能，可以编辑 `vehicle_data.py` 文件。脚本设计为易于扩展和维护。
