package com.ehome.oc.service;

import com.ehome.oc.service.impl.HouseInfoServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 房屋信息服务测试类
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HouseInfoServiceTest {

    private static final Logger log = LoggerFactory.getLogger(HouseInfoServiceTest.class);

    @Autowired
    private IHouseInfoService houseInfoService;

    /**
     * 测试刷新房屋全称字段
     */
    @Test
    public void testRefreshCombineName() {
        try {
            log.info("开始测试刷新房屋全称字段");
            
            // 测试刷新所有小区的房屋全称
            houseInfoService.refreshCombineName(null);
            
            log.info("刷新房屋全称字段测试完成");
        } catch (Exception e) {
            log.error("刷新房屋全称字段测试失败", e);
            throw e;
        }
    }

    /**
     * 测试刷新所有统计字段
     */
    @Test
    public void testRefreshAllStatistics() {
        try {
            log.info("开始测试刷新所有统计字段");
            
            // 测试刷新所有小区的统计字段
            houseInfoService.refreshAllStatistics(null);
            
            log.info("刷新所有统计字段测试完成");
        } catch (Exception e) {
            log.error("刷新所有统计字段测试失败", e);
            throw e;
        }
    }

    /**
     * 测试刷新指定小区的统计字段
     */
    @Test
    public void testRefreshStatisticsForSpecificCommunity() {
        try {
            log.info("开始测试刷新指定小区的统计字段");
            
            // 这里可以替换为实际的小区ID进行测试
            String testCommunityId = "test_community_id";
            
            // 测试刷新指定小区的统计字段
            houseInfoService.refreshAllStatistics(testCommunityId);
            
            log.info("刷新指定小区统计字段测试完成");
        } catch (Exception e) {
            log.error("刷新指定小区统计字段测试失败", e);
            // 这里不抛出异常，因为测试小区ID可能不存在
            log.warn("测试使用的小区ID可能不存在，这是正常的");
        }
    }
}
