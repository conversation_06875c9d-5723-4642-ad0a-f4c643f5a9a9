-- 修复 is_default 字段问题的SQL脚本
-- 问题：某些查询试图从 eh_house_info 表中获取 is_default 字段，但该字段只存在于 eh_house_owner_rel 表中
-- 解决方案：确保表结构正确，并提供数据修复脚本

-- =====================================================
-- 1. 检查当前表结构
-- =====================================================

-- 检查 eh_house_info 表结构
DESCRIBE eh_house_info;

-- 检查 eh_house_owner_rel 表结构  
DESCRIBE eh_house_owner_rel;

-- =====================================================
-- 2. 修复 eh_house_info 表结构（如果存在错误的 is_default 字段）
-- =====================================================

-- 如果 eh_house_info 表中错误地包含了 is_default 字段，则删除它
-- 注意：只有在确认该字段不应该存在时才执行此操作
-- ALTER TABLE eh_house_info DROP COLUMN IF EXISTS is_default;

-- =====================================================
-- 3. 确保 eh_house_owner_rel 表结构正确
-- =====================================================

-- 检查 eh_house_owner_rel 表是否包含所有必需字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'eh_house_owner_rel' 
ORDER BY ORDINAL_POSITION;

-- 如果 is_default 字段不存在，则添加它
-- ALTER TABLE eh_house_owner_rel ADD COLUMN is_default int(11) DEFAULT '0' COMMENT '是否默认（0否 1是）';

-- =====================================================
-- 4. 重建 eh_house_info 表（如果需要）
-- =====================================================

-- 如果需要完全重建 eh_house_info 表，使用以下结构：
/*
DROP TABLE IF EXISTS eh_house_info_backup;
CREATE TABLE eh_house_info_backup AS SELECT * FROM eh_house_info;

DROP TABLE IF EXISTS eh_house_info;
CREATE TABLE `eh_house_info` (
  `house_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '房屋ID',
  `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
  `community_name` varchar(50) DEFAULT NULL COMMENT '小区名称',
  `building_id` varchar(32) DEFAULT NULL COMMENT '楼栋ID',
  `building_name` varchar(20) DEFAULT NULL COMMENT '楼栋号',
  `unit_id` varchar(32) DEFAULT NULL COMMENT '单元ID',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单元名称',
  `combina_name` varchar(255) DEFAULT '' COMMENT '房屋全称',
  `floor` varchar(10) DEFAULT NULL COMMENT '楼层',
  `room` varchar(20) DEFAULT NULL COMMENT '房间号',
  `room_tag` varchar(30) DEFAULT '' COMMENT '房屋标签',
  `use_area` decimal(10,2) DEFAULT '0.00' COMMENT '使用面积',
  `total_area` decimal(10,2) DEFAULT '0.00' COMMENT '建筑面积',
  `house_type` varchar(10) DEFAULT '' COMMENT '房屋类型',
  `house_status` varchar(10) DEFAULT '' COMMENT '房屋状态',
  `check_status` int(11) DEFAULT '0' COMMENT '审核状态（0未审核 1已审核 2审核不通过）',
  `area` decimal(10,2) DEFAULT '0.00' COMMENT '房屋面积',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
  `owner_count` int(11) DEFAULT '0' COMMENT '绑定住户数量',
  `owner_str` varchar(255) DEFAULT '' COMMENT '关联住户',
  `arrear_amount` decimal(10,2) DEFAULT '0.00' COMMENT '欠费金额',
  `property_no` varchar(100) DEFAULT '',
  `house_tag` varchar(100) DEFAULT '',
  PRIMARY KEY (`house_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2000659 DEFAULT CHARSET=utf8mb4 COMMENT='房屋信息表';

-- 从备份表恢复数据（排除 is_default 字段）
INSERT INTO eh_house_info (
  house_id, community_id, community_name, building_id, building_name, 
  unit_id, unit_name, combina_name, floor, room, room_tag, 
  use_area, total_area, house_type, house_status, check_status, 
  area, remark, create_time, create_by, update_time, update_by, 
  owner_count, owner_str, arrear_amount, property_no, house_tag
)
SELECT 
  house_id, community_id, community_name, building_id, building_name, 
  unit_id, unit_name, combina_name, floor, room, room_tag, 
  use_area, total_area, house_type, house_status, check_status, 
  area, remark, create_time, create_by, update_time, update_by, 
  owner_count, IFNULL(owner_str, ''), IFNULL(arrear_amount, 0.00), 
  IFNULL(property_no, ''), IFNULL(house_tag, '')
FROM eh_house_info_backup;
*/

-- =====================================================
-- 5. 确保 eh_house_owner_rel 表结构正确
-- =====================================================

-- 重建 eh_house_owner_rel 表（如果需要）
/*
DROP TABLE IF EXISTS eh_house_owner_rel_backup;
CREATE TABLE eh_house_owner_rel_backup AS SELECT * FROM eh_house_owner_rel;

DROP TABLE IF EXISTS eh_house_owner_rel;
CREATE TABLE `eh_house_owner_rel` (
  `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
  `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `community_id` varchar(32) DEFAULT NULL,
  `rel_type` int(11) DEFAULT '1' COMMENT '关系类型（1业主 2家庭成员 3租户）',
  `is_default` int(11) DEFAULT '0' COMMENT '是否默认（0否 1是）',
  `check_status` int(11) DEFAULT '1' COMMENT '审核状态（0未审核 1已审核 2审核不通过）',
  `apply_flag` int(11) DEFAULT '0' COMMENT '是否申请的数据 0 否 1是',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
  `approve_info` json DEFAULT NULL COMMENT '审批信息JSON',
  `file_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`rel_id`),
  KEY `idx_house_id` (`house_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋业主关系表';

-- 从备份表恢复数据
INSERT INTO eh_house_owner_rel SELECT * FROM eh_house_owner_rel_backup;
*/

-- =====================================================
-- 6. 验证修复结果
-- =====================================================

-- 验证表结构
SELECT 'eh_house_info 表字段' as table_info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'eh_house_info' 
ORDER BY ORDINAL_POSITION;

SELECT 'eh_house_owner_rel 表字段' as table_info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'eh_house_owner_rel' 
ORDER BY ORDINAL_POSITION;

-- 验证数据完整性
SELECT 'eh_house_info 数据统计' as data_info;
SELECT COUNT(*) as total_houses FROM eh_house_info;

SELECT 'eh_house_owner_rel 数据统计' as data_info;
SELECT COUNT(*) as total_relations FROM eh_house_owner_rel;
SELECT COUNT(*) as default_relations FROM eh_house_owner_rel WHERE is_default = 1;

-- 检查是否还有错误的查询
SELECT 'is_default 字段使用检查' as check_info;
-- 这个查询应该成功，因为 is_default 存在于 eh_house_owner_rel 表中
SELECT COUNT(*) as relations_with_default 
FROM eh_house_owner_rel 
WHERE is_default = 1;

-- =====================================================
-- 7. 清理备份表（可选）
-- =====================================================

-- 确认数据正确后，可以删除备份表
-- DROP TABLE IF EXISTS eh_house_info_backup;
-- DROP TABLE IF EXISTS eh_house_owner_rel_backup;

-- =====================================================
-- 8. 修复说明
-- =====================================================

/*
修复总结：
1. eh_house_info 表不应该包含 is_default 字段
2. eh_house_owner_rel 表必须包含 is_default 字段
3. 所有查询 is_default 字段的地方都应该通过 JOIN eh_house_owner_rel 表来获取
4. data.py 脚本已修复，不再在 eh_house_info 表中生成 is_default 字段
5. 不生成 eh_vehicle 表相关数据（按用户要求）

正确的查询示例：
SELECT t1.*, t3.is_default user_default 
FROM eh_house_info t1 
INNER JOIN eh_house_owner_rel t3 ON t1.house_id = t3.house_id 
WHERE t3.owner_id = ?
*/
