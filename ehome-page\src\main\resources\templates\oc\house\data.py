import os
import json

def sql_str(val):
    if val is None:
        return 'NULL'
    if isinstance(val, (int, float)):
        return str(val)
    return "'{}'".format(str(val).replace("'", "''"))

def main():
    # 获取脚本当前目录
    base_dir = os.path.dirname(__file__)
    data_file = os.path.join(base_dir, 'getdata.txt')
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"未找到 {data_file}，请确认 data.txt 和 data.py 在同一目录下。")
        return

    house_sqls = []
    # 移除 vehicle_sqls，不生成 eh_vehicle 表数据
    owner_rel_sqls = []

    for line in lines:
        if not line.strip():
            continue
        try:
            obj = json.loads(line)
        except Exception as e:
            print(f"解析JSON失败: {e}\n内容: {line[:100]}")
            continue
        house_list = obj.get('data', {}).get('list', [])
        for house in house_list:
            # eh_house_info (修复：移除 is_default 字段，该字段只存在于 eh_house_owner_rel 表中)
            house_id = house.get('id')
            community_id = house.get('communityID', '')
            community_name = house.get('communityName', '')
            building_id = house.get('buildingID', '')
            building_name = house.get('buildingName', '')
            unit_id = house.get('unitID', '')
            unit_name = house.get('unitName', '')
            combina_name = house.get('combinaName', '')
            floor = house.get('layer', '')
            room = house.get('name', '')
            room_tag = ''
            use_area = house.get('usableSize', 0)
            total_area = house.get('buildingSize', 0)
            house_type = house.get('houseTypeName', '')
            house_status = house.get('houseStatusName', '')
            check_status = 1 if house_status == '已入住' else 0
            area = total_area
            remark = ''
            create_time = ''
            create_by = ''
            update_time = ''
            update_by = ''
            owner_count = house.get('userNum', 0)
            # 添加新字段
            owner_str = ''
            arrear_amount = 0.00
            property_no = ''
            house_tag = ''

            # 修复后的SQL：移除了 is_default 字段，添加了缺失的字段
            house_sql = f"INSERT INTO eh_house_info (house_id, community_id, community_name, building_id, building_name, unit_id, unit_name, combina_name, floor, room, room_tag, use_area, total_area, house_type, house_status, check_status, area, remark, create_time, create_by, update_time, update_by, owner_count, owner_str, arrear_amount, property_no, house_tag) VALUES ({sql_str(house_id)}, {sql_str(community_id)}, {sql_str(community_name)}, {sql_str(building_id)}, {sql_str(building_name)}, {sql_str(unit_id)}, {sql_str(unit_name)}, {sql_str(combina_name)}, {sql_str(floor)}, {sql_str(room)}, {sql_str(room_tag)}, {sql_str(use_area)}, {sql_str(total_area)}, {sql_str(house_type)}, {sql_str(house_status)}, {sql_str(check_status)}, {sql_str(area)}, {sql_str(remark)}, {sql_str(create_time)}, {sql_str(create_by)}, {sql_str(update_time)}, {sql_str(update_by)}, {sql_str(owner_count)}, {sql_str(owner_str)}, {sql_str(arrear_amount)}, {sql_str(property_no)}, {sql_str(house_tag)});"
            house_sqls.append(house_sql)

            # 移除 eh_vehicle 相关代码（按用户要求不生成 eh_vehicle 表数据）

            # eh_house_owner_rel
            user_list = house.get('userList') or []
            for user in user_list:
                rel_id = f"{house_id}_{user.get('id')}"
                owner_id = user.get('id')
                rel_type = user.get('itemType', 1)
                is_default = 1 if rel_type == 1 else 0
                check_status = 1
                remark = ''
                create_time = ''
                create_by = ''
                update_time = ''
                update_by = ''
                owner_rel_sql = f"INSERT INTO eh_house_owner_rel (rel_id, house_id, owner_id, community_id, rel_type, is_default, check_status, remark, create_time, create_by, update_time, update_by) VALUES ({sql_str(rel_id)}, {sql_str(house_id)}, {sql_str(owner_id)}, {sql_str(community_id)}, {sql_str(rel_type)}, {sql_str(is_default)}, {sql_str(check_status)}, {sql_str(remark)}, {sql_str(create_time)}, {sql_str(create_by)}, {sql_str(update_time)}, {sql_str(update_by)});"
                owner_rel_sqls.append(owner_rel_sql)

    # 输出所有 SQL（移除 vehicle_sqls）
    output_file = os.path.join(base_dir, 'output.sql')
    with open(output_file, 'w', encoding='utf-8') as f:
        # 先输出房屋信息
        for sql in house_sqls:
            f.write(sql + '\n')
        # 再输出房屋业主关系
        for sql in owner_rel_sqls:
            f.write(sql + '\n')

    print(f"生成完成！")
    print(f"房屋信息SQL: {len(house_sqls)} 条")
    print(f"房屋业主关系SQL: {len(owner_rel_sqls)} 条")
    print(f"输出文件: {output_file}")

if __name__ == '__main__':
    main()