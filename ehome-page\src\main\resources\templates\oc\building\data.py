import os
import json
from datetime import datetime

def sql_str(val):
    """处理SQL字符串，防止SQL注入"""
    if val is None:
        return 'NULL'
    if isinstance(val, (int, float)):
        return str(val)
    return "'{}'".format(str(val).replace("'", "''"))

def main():
    # 获取脚本当前目录
    base_dir = os.path.dirname(__file__)
    data_file = os.path.join(base_dir, 'data.txt')
    output_file = os.path.join(base_dir, 'out.sql')
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"未找到 {data_file}，请确认 data.txt 和 data.py 在同一目录下。")
        return
    
    try:
        data = json.loads(content)
    except Exception as e:
        print(f"解析JSON失败: {e}")
        return
    
    # 提取基本信息
    community_data = data.get('data', {})
    community_id = community_data.get('communityID', '')
    community_name = community_data.get('communityName', '')
    building_list = community_data.get('buildingList', [])
    
    building_sqls = []
    unit_sqls = []
    
    # 当前时间作为创建时间
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    for building in building_list:
        # 处理楼栋信息
        building_id = building.get('id')
        building_name = building.get('name', '')
        alias_name = building.get('orderName', '')
        unit_list = building.get('unitList', [])
        total_units = len(unit_list)
        
        # 生成eh_building的INSERT语句
        building_sql = f"""INSERT INTO eh_building (building_id, community_id, community_name, name, alias_name, total_units, create_by, create_time, update_by, update_time, remark, manager, house_count, house_area, order_index) VALUES ({sql_str(building_id)}, {sql_str(community_id)}, {sql_str(community_name)}, {sql_str(building_name)}, {sql_str(alias_name)}, {sql_str(total_units)}, {sql_str('system')}, {sql_str(current_time)}, {sql_str('system')}, {sql_str(current_time)}, {sql_str('')}, {sql_str('')}, {sql_str(0)}, {sql_str(0.00)}, {sql_str(100)});"""
        
        building_sqls.append(building_sql)
        
        # 处理单元信息
        for unit in unit_list:
            unit_id = unit.get('id')
            unit_building_id = unit.get('buildingID')
            unit_community_id = unit.get('communityID')
            unit_name = unit.get('name', '')
            
            # 生成eh_unit的INSERT语句
            unit_sql = f"""INSERT INTO eh_unit (unit_id, building_id, community_id, name, house_count, house_area, create_by, create_time, update_by, update_time, remark) VALUES ({sql_str(unit_id)}, {sql_str(unit_building_id)}, {sql_str(unit_community_id)}, {sql_str(unit_name)}, {sql_str(0)}, {sql_str(0.00)}, {sql_str('system')}, {sql_str(current_time)}, {sql_str('system')}, {sql_str(current_time)}, {sql_str('')});"""
            
            unit_sqls.append(unit_sql)
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- 楼栋信息表数据\n")
            for sql in building_sqls:
                f.write(sql + '\n')
            
            f.write("\n-- 单元信息表数据\n")
            for sql in unit_sqls:
                f.write(sql + '\n')
        
        print(f"成功生成SQL文件: {output_file}")
        print(f"共生成 {len(building_sqls)} 条楼栋记录")
        print(f"共生成 {len(unit_sqls)} 条单元记录")
        
    except Exception as e:
        print(f"写入文件失败: {e}")

if __name__ == '__main__':
    main()
