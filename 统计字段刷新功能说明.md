# 统计字段刷新功能说明

## 功能概述

通过业主和房屋关联表（`eh_house_owner_rel`）刷新业主表、车辆表、房屋表、车位表的统计字段和字符串字段，确保数据一致性。

## 实现的功能

### 1. 刷新房屋全称字段（combina_name）
- **接口**: `IHouseInfoService.refreshCombineName(String communityId)`
- **功能**: 根据楼栋名称和单元名称生成房屋全称
- **格式**: "楼栋名-单元名" 或 "楼栋名"（当单元名为空时）

### 2. 统一刷新所有统计字段
- **接口**: `IHouseInfoService.refreshAllStatistics(String communityId)`
- **功能**: 按顺序刷新所有相关表的统计字段和字符串字段

## 刷新的字段详情

### 业主表（eh_owner）
**统计字段**:
- `house_count`: 房屋数量
- `parking_count`: 车位数量  
- `car_count`: 车辆数量

**字符串字段**:
- `house_info`: 绑定房产信息（格式：楼栋-单元-房间号）
- `parking_no`: 绑定车位号（多个用逗号分隔）
- `car_info`: 关联车牌（多个用逗号分隔）

### 房屋表（eh_house_info）
**统计字段**:
- `owner_count`: 绑定住户数量

**字符串字段**:
- `owner_str`: 关联住户姓名（多个用逗号分隔）
- `combina_name`: 房屋全称

### 车位表（eh_parking_space）
**统计字段**:
- `owner_count`: 绑定业主数

**字符串字段**:
- `owner_name`: 业主名称（多个用逗号分隔）

### 车辆表（eh_vehicle）
**统计字段**:
- `owner_count`: 绑定业主数

**字符串字段**:
- `owner_name`: 业主名称（格式：姓名(手机号)，多个用逗号分隔）

## API接口

### 1. 刷新所有统计字段
```
POST /oc/house/refreshStatistics
```
- **功能**: 刷新当前用户所在小区的所有统计字段
- **权限**: 需要登录
- **返回**: 成功/失败消息

### 2. 刷新房屋全称
```
POST /oc/house/refreshCombineName
```
- **功能**: 刷新当前用户所在小区的房屋全称字段
- **权限**: 需要登录
- **返回**: 成功/失败消息

### 3. 楼栋统计刷新（包含全量统计刷新）
```
POST /oc/building/refreshStats
```
- **功能**: 刷新楼栋统计信息，同时调用全量统计字段刷新
- **权限**: 需要登录
- **返回**: 成功/失败消息
- **说明**: 该接口会先刷新楼栋的房屋数量和面积统计，然后调用完整的统计字段刷新功能

## 使用方式

### 1. 通过Web接口调用
```javascript
// 刷新所有统计字段
$.post('/oc/house/refreshStatistics', function(result) {
    if (result.code === 0) {
        alert('统计字段刷新成功');
    } else {
        alert('刷新失败: ' + result.msg);
    }
});

// 刷新房屋全称
$.post('/oc/house/refreshCombineName', function(result) {
    if (result.code === 0) {
        alert('房屋全称刷新成功');
    } else {
        alert('刷新失败: ' + result.msg);
    }
});

// 楼栋统计刷新（包含全量统计刷新）
$.post('/oc/building/refreshStats', function(result) {
    if (result.code === 0) {
        alert('楼栋统计和全量统计刷新成功');
    } else {
        alert('刷新失败: ' + result.msg);
    }
});
```

### 2. 通过Service直接调用
```java
@Autowired
private IHouseInfoService houseInfoService;

// 刷新指定小区的所有统计字段
houseInfoService.refreshAllStatistics("community_id");

// 刷新所有小区的统计字段
houseInfoService.refreshAllStatistics(null);

// 只刷新房屋全称
houseInfoService.refreshCombineName("community_id");
```

## 注意事项

1. **事务性**: `refreshAllStatistics` 方法使用了 `@Transactional` 注解，确保数据一致性
2. **性能**: 大数据量时可能耗时较长，建议在业务低峰期执行
3. **日志**: 所有操作都有详细的日志记录，包括执行时间
4. **错误处理**: 任何步骤失败都会回滚事务并抛出异常
5. **权限**: Web接口会自动获取当前用户的小区ID，只刷新该小区的数据

## 执行顺序

1. 刷新房屋全称（combina_name）
2. 刷新业主表统计字段
3. 刷新房屋表统计字段  
4. 刷新车位表统计字段
5. 刷新车辆表统计字段

## 测试

项目包含了完整的单元测试：
- `HouseInfoServiceTest.testRefreshCombineName()`: 测试房屋全称刷新
- `HouseInfoServiceTest.testRefreshAllStatistics()`: 测试所有统计字段刷新
- `HouseInfoServiceTest.testRefreshStatisticsForSpecificCommunity()`: 测试指定小区刷新
