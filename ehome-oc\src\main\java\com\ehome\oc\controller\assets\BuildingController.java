package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.oc.service.IBuildingService;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 楼栋信息Controller
 */
@Controller
@RequestMapping("/oc/building")
public class BuildingController extends BaseController {

    private static final String PREFIX = "oc/building";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @Autowired
    private IBuildingService buildingService;

    @Autowired
    private IHouseInfoService houseInfoService;

    @GetMapping("/tree")
    @ResponseBody
    public AjaxResult getBuildingUnitTree() {
        try {
            String communityId = getSysUser().getCommunityId();
            List<Map<String, Object>> tree = buildingService.getBuildingUnitTree(communityId);
            return AjaxResult.success("获取楼栋单元树成功", tree);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return AjaxResult.error("获取楼栋单元树失败: " + e.getMessage());
        }
    }

    @GetMapping("/edit/{buildingId}")
    public String edit(@PathVariable("buildingId") String buildingId, ModelMap mmap) {
        Record building = Db.findFirst("select * from eh_building where building_id = ?", buildingId);
        mmap.put("building", building.toMap());
        return PREFIX + "/edit";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String buildingId = params.getString("building_id");
        if (StringUtils.isEmpty(buildingId)) {
            return AjaxResult.error("楼栋ID不能为空");
        }
        Record building = Db.findFirst("select * from eh_building where building_id = ?", buildingId);
        return AjaxResult.success(null, building.toMap());
    }

    @Log(title = "新增楼栋", businessType = BusinessType.INSERT)
    @PostMapping("/addData")
    @ResponseBody
    public AjaxResult addData() {
        JSONObject params = getParams();
        String name = params.getString("name");
        String communityId = getSysUser().getCommunityId();

        // 检查楼栋名称是否重复
        if (!StringUtils.isEmpty(name)) {
            Record existingBuilding = Db.findFirst("SELECT * FROM eh_building WHERE name = ? AND community_id = ?", name, communityId);
            if (existingBuilding != null) {
                return AjaxResult.error("楼栋名称已存在");
            }
        }

        Record building = new Record();
        building.setColumns(params);
        building.remove("building_id");
        setCreateAndUpdateInfo(building);
        Db.save("eh_building","building_id",building);
        return AjaxResult.success();
    }

    @Log(title = "修改楼栋", businessType = BusinessType.UPDATE)
    @PostMapping("/editSave")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        String name = params.getString("name");
        String buildingId = params.getString("building_id");
        String communityId = getSysUser().getCommunityId();

        // 检查楼栋名称是否重复（排除自己）
        if (!StringUtils.isEmpty(name)) {
            Record existingBuilding = Db.findFirst("SELECT * FROM eh_building WHERE name = ? AND community_id = ? AND building_id != ?", name, communityId, buildingId);
            if (existingBuilding != null) {
                return AjaxResult.error("楼栋名称已存在");
            }
        }

        Record building = new Record();
        building.setColumns(params);
        setUpdateInfo(building);
        return toAjax(Db.update("eh_building","building_id",building));
    }

    @Log(title = "删除楼栋", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");

        try {
            for (String id : idArr) {
                // 获取楼栋名称用于错误提示
                Record building = Db.findFirst("SELECT name FROM eh_building WHERE building_id = ?", id);
                String buildingName = building != null ? building.getStr("name") : "未知楼栋";

                // 获取楼栋下的所有单元
                List<Record> units = Db.find("SELECT unit_id, name FROM eh_unit WHERE building_id = ?", id);

                // 检查每个单元下是否有房屋数据
                for (Record unit : units) {
                    String unitId = unit.getStr("unit_id");
                    String unitName = unit.getStr("name");
                    int houseCount = Db.queryInt("SELECT COUNT(*) as count FROM eh_house_info WHERE unit_id = ?", unitId);
                    if (houseCount > 0) {
                        return error("楼栋【" + buildingName + "】下的单元【" + unitName + "】存在房屋数据，无法删除");
                    }
                }

                // 先删除楼栋下的所有单元（级联删除）
                if (units != null && !units.isEmpty()) {
                    logger.info("删除楼栋 {} 下的 {} 个单元", id, units.size());
                    for (Record unit : units) {
                        String unitId = unit.getStr("unit_id");
                        Db.deleteById("eh_unit", "unit_id", unitId);
                    }
                }

                // 删除楼栋
                Db.deleteById("eh_building", "building_id", id);
                logger.info("成功删除楼栋: {}", id);
            }
            return success();
        } catch (Exception e) {
            logger.error("删除楼栋失败: " + e.getMessage(), e);
            return error("删除楼栋失败: " + e.getMessage());
        }
    }

    /**
     * 批量楼栋排序修改
     */
    @PostMapping("/updateBatchSort")
    @ResponseBody
    public AjaxResult updateBatchSort(){
        JSONObject params = getParams();
        String buildingIds = params.getString("buildingIds");
        String orderIndexes = params.getString("orderIndexes");
        if (StringUtils.isEmpty(buildingIds)) {
            return AjaxResult.error("楼栋ID不能为空");
        }
        if (StringUtils.isEmpty(orderIndexes)) {
            return AjaxResult.error("排序值不能为空");
        }

        String[] buildingIdArr = buildingIds.split(",");
        String[] orderIndexArr = orderIndexes.split(",");

        if (buildingIdArr.length != orderIndexArr.length) {
            return AjaxResult.error("楼栋ID和排序值数量不匹配");
        }

        try {
            String communityId = getSysUser().getCommunityId();
            for (int i = 0; i < buildingIdArr.length; i++) {
                Integer orderIndexValue = Integer.parseInt(orderIndexArr[i]);

                // 验证楼栋是否属于当前社区
                Record existingBuilding = Db.findFirst("SELECT * FROM eh_building WHERE building_id = ? AND community_id = ?",
                    buildingIdArr[i], communityId);
                if (existingBuilding == null) {
                    return AjaxResult.error("楼栋不存在或无权限修改");
                }

                Record record = new Record();
                record.set("building_id", buildingIdArr[i]);
                record.set("order_index", orderIndexValue);
                record.set("update_time", DateUtils.getTime());
                record.set("update_by", getLoginName());
                Db.update("eh_building", "building_id", record);
            }
            return AjaxResult.success("批量排序修改成功");
        } catch (NumberFormatException e) {
            return AjaxResult.error("排序值必须为数字");
        } catch (Exception e) {
            logger.error("批量排序修改失败", e);
            return AjaxResult.error("批量排序修改失败: " + e.getMessage());
        }
    }

    @Log(title = "批量导入楼栋", businessType = BusinessType.INSERT)
    @PostMapping("/batchImport")
    @ResponseBody
    public AjaxResult batchImport(@RequestBody Map<String, Object> requestData) {
        @SuppressWarnings("unchecked")
        List<Map<String, String>> importData = (List<Map<String, String>>) requestData.get("importData");

        if (importData == null || importData.isEmpty()) {
            return AjaxResult.error("导入数据不能为空");
        }

        String communityId = getSysUser().getCommunityId();
        String loginName = getSysUser().getLoginName();
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);

        int successCount = 0;
        int skipCount = 0;

        try {
            // 查询当前小区最大的 order_index 值，用于设置新楼栋的排序
            Record maxOrderRecord = Db.findFirst("SELECT MAX(order_index) as max_order FROM eh_building WHERE community_id = ?", communityId);
            int currentOrder = maxOrderRecord != null && maxOrderRecord.get("max_order") != null ?
                maxOrderRecord.getInt("max_order") + 10 : 100;

            for (Map<String, String> item : importData) {
                String buildingName = item.get("buildingName");

                if (StringUtils.isEmpty(buildingName)) {
                    skipCount++;
                    continue;
                }

                // 检查楼栋名称是否已存在
                Record existingRecord = Db.findFirst("SELECT * FROM eh_building WHERE name = ? AND community_id = ?", buildingName, communityId);
                if (existingRecord != null) {
                    skipCount++;
                    continue;
                }

                // 创建新记录
                Record building = new Record();
                building.set("community_id", communityId);
                building.set("name", buildingName);
                building.set("alias_name", "");
                building.set("total_units", 0);
                building.set("house_count", 0);
                building.set("house_area", 0.00);
                building.set("manager", "");
                building.set("create_time", now);
                building.set("update_time", now);
                building.set("create_by", loginName);
                building.set("update_by", loginName);
                building.set("remark", "批量导入");
                building.set("order_index", currentOrder);

                Db.save("eh_building", "building_id", building);
                currentOrder += 10; // 递增排序值，步长为10
                successCount++;
            }

            String message = String.format("导入完成！成功导入 %d 个楼栋", successCount);
            if (skipCount > 0) {
                message += String.format("，跳过重复 %d 个", skipCount);
            }

            return AjaxResult.success(message);
        } catch (Exception e) {
            logger.error("批量导入楼栋失败: " + e.getMessage(), e);
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    @Log(title = "批量导入楼栋单元", businessType = BusinessType.INSERT)
    @PostMapping("/batchImportUnit")
    @ResponseBody
    public AjaxResult batchImportUnit(@RequestBody Map<String, Object> requestData) {
        @SuppressWarnings("unchecked")
        List<Map<String, String>> importData = (List<Map<String, String>>) requestData.get("importData");

        if (importData == null || importData.isEmpty()) {
            return AjaxResult.error("导入数据不能为空");
        }

        String communityId = getSysUser().getCommunityId();
        String loginName = getSysUser().getLoginName();
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);

        int successCount = 0;
        int skipCount = 0;

        try {
            for (Map<String, String> item : importData) {
                String buildingName = item.get("buildingName");
                String unitName = item.get("unitName");

                if (StringUtils.isEmpty(buildingName) || StringUtils.isEmpty(unitName)) {
                    skipCount++;
                    continue;
                }

                // 查找楼栋ID
                Record buildingRecord = Db.findFirst("SELECT building_id FROM eh_building WHERE name = ? AND community_id = ?", buildingName, communityId);
                if (buildingRecord == null) {
                    skipCount++;
                    continue; // 楼栋不存在，跳过
                }

                String buildingId = buildingRecord.getStr("building_id");

                // 检查单元是否已存在（同一小区同一楼栋内）
                Record existingUnit = Db.findFirst("SELECT * FROM eh_unit WHERE name = ? AND building_id = ? AND community_id = ?", unitName, buildingId, communityId);
                if (existingUnit != null) {
                    skipCount++;
                    continue; // 单元已存在，跳过
                }

                // 创建新单元记录
                Record unit = new Record();
                unit.set("building_id", buildingId);
                unit.set("community_id", communityId);
                unit.set("name", unitName);
                unit.set("house_count", 0);
                unit.set("house_area", 0.00);
                unit.set("create_time", now);
                unit.set("update_time", now);
                unit.set("create_by", loginName);
                unit.set("update_by", loginName);
                unit.set("remark", "批量导入");

                Db.save("eh_unit", "unit_id", unit);
                successCount++;
            }

            // 更新楼栋的单元总数
            updateBuildingTotalUnits(communityId);

            String message = String.format("导入完成！成功导入 %d 个单元", successCount);
            if (skipCount > 0) {
                message += String.format("，跳过 %d 个（楼栋不存在或单元重复）", skipCount);
            }

            return AjaxResult.success(message);
        } catch (Exception e) {
            logger.error("批量导入楼栋单元失败: " + e.getMessage(), e);
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 更新楼栋的单元总数
     */
    private void updateBuildingTotalUnits(String communityId) {
        try {
            // 查询所有楼栋
            List<Record> buildings = Db.find("SELECT building_id FROM eh_building WHERE community_id = ?", communityId);

            for (Record building : buildings) {
                String buildingId = building.getStr("building_id");

                // 统计该楼栋的单元数量
                Record stats = Db.findFirst("SELECT COUNT(*) as unit_count FROM eh_unit WHERE building_id = ?", buildingId);

                if (stats != null) {
                    // 更新楼栋单元总数
                    Db.update("UPDATE eh_building SET total_units = ?, update_time = ?, update_by = ? WHERE building_id = ?",
                            stats.getInt("unit_count"),
                            DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS),
                            getSysUser().getLoginName(),
                            buildingId);
                }
            }
        } catch (Exception e) {
            logger.error("更新楼栋单元总数失败: " + e.getMessage(), e);
        }
    }

    @Log(title = "刷新楼栋统计", businessType = BusinessType.UPDATE)
    @PostMapping("/refreshStats")
    @ResponseBody
    public AjaxResult refreshStats() {
        try {
            String communityId = getSysUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("小区ID不能为空");
            }

            // 查询所有楼栋
            List<Record> buildings = Db.find("SELECT building_id FROM eh_building WHERE community_id = ?", communityId);

            int updatedCount = 0;
            for (Record building : buildings) {
                String buildingId = building.getStr("building_id");

                // 统计该楼栋的房屋数量和面积
                Record stats = Db.findFirst(
                    "SELECT COUNT(*) as house_count, COALESCE(SUM(total_area), 0) as house_area " +
                    "FROM eh_house_info WHERE building_id = ?",
                    buildingId
                );

                if (stats != null) {
                    // 更新楼栋统计信息
                    int result = Db.update(
                        "UPDATE eh_building SET house_count = ?, house_area = ?, update_time = NOW(), update_by = ? WHERE building_id = ?",
                        stats.getInt("house_count"),
                        stats.getBigDecimal("house_area"),
                        getSysUser().getLoginName(),
                        buildingId
                    );
                    if (result > 0) {
                        updatedCount++;
                    }
                }
            }

            // 刷新所有相关统计字段
            houseInfoService.refreshAllStatistics(communityId);

            return AjaxResult.success("成功刷新 " + updatedCount + " 个楼栋的统计信息，并刷新了所有相关统计字段");
        } catch (Exception e) {
            logger.error("刷新楼栋统计失败", e);
            return AjaxResult.error("刷新失败: " + e.getMessage());
        }
    }

    @PostMapping("/checkName")
    @ResponseBody
    public boolean checkName() {
        JSONObject params = getParams();
        String name = params.getString("name");
        String communityId = getSysUser().getCommunityId();
        String buildingId = params.getString("building_id");
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(communityId)) {
            return false;
        }
        EasySQL sql = new EasySQL();
        sql.append("select * from eh_building where 1=1");
        sql.append(name, "and name = ?");
        sql.append(buildingId, "and building_id != ?");
        sql.append(communityId, "and community_id = ?");
        Record building = Db.findFirst(sql.getSQL(), sql.getParams());
        return building == null;
    }

    @PostMapping("/queryCommunity")
    @ResponseBody
    public AjaxResult queryCommunity() {
        List<Record> list = Db.find("select * from eh_community where pms_id = ?", getSysUser().getPmsId());
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            map.put(record.getStr("oc_id"), record.getStr("oc_name"));
        });
        return AjaxResult.success(map);
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_building t1");
        sql.append("where 1=1");
        
        sql.append(getSysUser().getCommunityId(), "and t1.community_id = ?");
        sql.appendLike(params.getString("name"), "and t1.name like ?");
        sql.appendLike(params.getString("alias_name"), "and t1.alias_name like ?");
        sql.append(params.getString("total_units"), "and t1.total_units = ?");
        sql.appendLike(params.get("manager"), "and t1.manager like ?");
        
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and t1.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and t1.create_time <= ?");

        sql.append("order by t1.order_index asc");
        return sql;
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
        record.set("community_id", getSysUser().getCommunityId());
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }
}